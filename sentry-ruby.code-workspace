{
  "folders": [
    {
      "name": "root",
      "path": "."
    },
    {
      "path": "sentry-ruby"
    },
    {
      "path": "sentry-rails"
    },
    {
      "path": "sentry-sidekiq"
    },
    {
      "path": "sentry-delayed_job"
    },
    {
      "path": "sentry-resque"
    },
    {
      "path": "sentry-opentelemetry"
    },
    {
      "path": "sentry-bench"
    },
  ],
  "settings": {
    "files.exclude": {
      "sentry-delayed_job": true,
      "sentry-rails": true,
      "sentry-resque": true,
      "sentry-ruby": true,
      "sentry-sidekiq": true,
      "sentry-opentelemetry": true
    },
    "rubyLsp.formatter": "rubocop",
    "rubyLsp.rubyVersionManager": {
      "identifier": "auto"
    }
  },
  "extensions": {
    "recommendations": [
      "Shopify.ruby-lsp"
    ]
  }
}
