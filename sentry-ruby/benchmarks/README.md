# Sentry Ruby Memory Benchmarking Tools

This directory contains tools for profiling and comparing memory usage in Sentry Ruby, particularly for logging functionality.

## Tools

### 1. Memory Profiler (`logging_memory_profile.rb`)

Profiles memory usage during logging operations and generates detailed reports.

**Usage:**
```bash
# Run with default 1000 iterations
bundle exec ruby benchmarks/logging_memory_profile.rb

# Run with custom iteration count
ITERATIONS=500 bundle exec ruby benchmarks/logging_memory_profile.rb
```

**Features:**
- Captures detailed memory allocation and retention data
- Stores results with git commit SHA for historical tracking
- Generates comprehensive HTML reports
- Tracks memory usage by object class and code location
- Provides performance recommendations

**Output:**
- HTML report: `tmp/logging_memory_profile.html`
- Raw data: `benchmarks/data/{short_sha}_{timestamp}.json`
- Latest data: `benchmarks/data/latest.json`

### 2. Memory Comparison Tool (`logging_memory_comparison.rb`)

Compares memory usage between two benchmark runs to identify improvements and regressions.

**Usage:**
```bash
# Compare last two benchmark runs automatically
bundle exec ruby benchmarks/logging_memory_comparison.rb

# Compare specific benchmark files
bundle exec ruby benchmarks/logging_memory_comparison.rb baseline.json current.json
```

**Features:**
- Automatic detection of latest benchmark files
- Side-by-side comparison of memory metrics
- Color-coded change indicators (🟢 improvement, 🔴 regression, 🟡 neutral)
- Detailed breakdown by object class and allocation location
- Git commit information for both runs

**Output:**
- HTML comparison report: `tmp/logging_memory_comparison.html`
- Console summary with key metrics

## Data Storage

Benchmark data is stored in `benchmarks/data/` with the following structure:

```
benchmarks/data/
├── {short_sha}_{timestamp}.json  # Individual benchmark runs
├── latest.json                   # Most recent run (symlink-like)
```

Each JSON file contains:
- **Metadata**: Git info, timestamp, iterations, execution time, versions
- **Memory Profile**: Allocation/retention data by class and location
- **Transport Stats**: Sentry-specific transport statistics

## Workflow Examples

### Basic Memory Profiling
```bash
# Run a baseline measurement
bundle exec ruby benchmarks/logging_memory_profile.rb

# Make code changes...

# Run another measurement
bundle exec ruby benchmarks/logging_memory_profile.rb

# Compare the results
bundle exec ruby benchmarks/logging_memory_comparison.rb
```

### Performance Testing Workflow
```bash
# Before making changes
git checkout main
ITERATIONS=1000 bundle exec ruby benchmarks/logging_memory_profile.rb

# Switch to feature branch
git checkout feature/memory-optimization
ITERATIONS=1000 bundle exec ruby benchmarks/logging_memory_profile.rb

# Compare results
bundle exec ruby benchmarks/logging_memory_comparison.rb
```

### Continuous Monitoring
```bash
# Add to CI/CD pipeline
ITERATIONS=500 bundle exec ruby benchmarks/logging_memory_profile.rb
bundle exec ruby benchmarks/logging_memory_comparison.rb
```

## Understanding the Reports

### Memory Profile Report
- **Executive Summary**: High-level memory usage metrics
- **Transport Statistics**: Sentry-specific event/envelope counts
- **Memory Breakdown**: Object allocation by class type
- **Allocation Locations**: Top code locations allocating memory
- **Retention Analysis**: Memory leaks and retained objects
- **File Analysis**: Memory usage by source file
- **Recommendations**: Automated performance suggestions

### Comparison Report
- **Overview**: Side-by-side commit and configuration info
- **Memory Comparison**: Key metrics with change indicators
- **Class Comparison**: Object allocation changes by type
- **Location Comparison**: Code location allocation changes
- **Commit Details**: Full git information for both runs

## Interpreting Results

### Change Indicators
- 🟢 **Improvement**: >5% reduction in memory usage
- 🔴 **Regression**: >5% increase in memory usage  
- 🟡 **Neutral**: <5% change (within noise threshold)

### Key Metrics to Watch
- **Total Allocated Memory**: Overall memory footprint
- **Total Retained Memory**: Potential memory leaks
- **Memory per Iteration**: Efficiency per operation
- **String Allocations**: Often the largest contributor
- **Hash Allocations**: Second largest contributor typically

### Performance Tips
- Look for high string allocation rates (>30% of total)
- Monitor retention rates (should be <10%)
- Focus on top allocation locations for optimization
- Use frozen strings and pre-allocated hashes where possible

## File Structure

```
benchmarks/
├── README.md                           # This file
├── logging_memory_profile.rb           # Main profiling tool
├── logging_memory_comparison.rb        # Comparison tool
├── test_logging_memory.rb             # Basic test script
├── data/                              # Stored benchmark data
│   ├── {sha}_{timestamp}.json         # Individual runs
│   └── latest.json                    # Latest run
└── tmp/                               # Generated reports (gitignored)
    ├── logging_memory_profile.html    # Latest profile report
    └── logging_memory_comparison.html # Latest comparison report
```

## Dependencies

- `memory_profiler` gem for memory profiling
- `json` for data serialization
- `fileutils` for file operations
- `sentry-ruby` and `sentry/benchmarks/benchmark_transport` for testing

## Git Integration

The tools automatically capture git information for each benchmark run:
- Commit SHA (full and short)
- Branch name
- Commit message and author
- Commit date

This enables tracking memory usage changes across different commits and branches.
