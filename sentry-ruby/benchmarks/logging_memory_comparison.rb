#!/usr/bin/env ruby
# frozen_string_literal: true

# Sentry Logging Memory Comparison Tool
#
# This script compares memory usage between two benchmark runs and generates
# a comprehensive comparison report showing improvements and regressions.
#
# Usage: 
#   ruby benchmarks/logging_memory_comparison.rb [baseline_file] [current_file]
#   ruby benchmarks/logging_memory_comparison.rb  # Uses last two runs

require 'json'
require 'fileutils'
require 'time'

# Helper methods for formatting and calculations
def format_bytes(bytes)
  return "0 B" if bytes == 0

  units = ['B', 'KB', 'MB', 'GB']
  size = bytes.to_f
  unit_index = 0

  while size >= 1024 && unit_index < units.length - 1
    size /= 1024
    unit_index += 1
  end

  "#{size.round(2)} #{units[unit_index]}"
end

def format_change(old_value, new_value, format_as_bytes: false)
  return "N/A" if old_value == 0
  
  change = new_value - old_value
  percentage = ((change.to_f / old_value) * 100).round(2)
  
  if format_as_bytes
    change_str = format_bytes(change.abs)
    change_str = change >= 0 ? "+#{change_str}" : "-#{change_str}"
  else
    change_str = change >= 0 ? "+#{change}" : change.to_s
  end
  
  color = if percentage > 5
    "🔴" # Regression
  elsif percentage < -5
    "🟢" # Improvement
  else
    "🟡" # Neutral
  end
  
  "#{color} #{change_str} (#{percentage >= 0 ? '+' : ''}#{percentage}%)"
end

def load_benchmark_data(filepath)
  unless File.exist?(filepath)
    puts "Error: File not found: #{filepath}"
    exit 1
  end
  
  JSON.parse(File.read(filepath))
rescue JSON::ParserError => e
  puts "Error: Invalid JSON in file #{filepath}: #{e.message}"
  exit 1
end

def find_latest_benchmark_files
  data_dir = File.join('benchmarks', 'data')
  
  unless Dir.exist?(data_dir)
    puts "Error: No benchmark data directory found at #{data_dir}"
    puts "Run the memory profiler first: ruby benchmarks/logging_memory_profile.rb"
    exit 1
  end
  
  json_files = Dir.glob(File.join(data_dir, '*.json'))
                  .reject { |f| File.basename(f) == 'latest.json' }
                  .sort_by { |f| File.mtime(f) }
  
  if json_files.length < 2
    puts "Error: Need at least 2 benchmark files to compare"
    puts "Found files: #{json_files.map { |f| File.basename(f) }}"
    puts "Run the memory profiler at least twice: ruby benchmarks/logging_memory_profile.rb"
    exit 1
  end
  
  # Return the two most recent files
  [json_files[-2], json_files[-1]]
end

def generate_comparison_report(baseline_data, current_data)
  baseline_meta = baseline_data['metadata']
  current_meta = current_data['metadata']
  baseline_profile = baseline_data['memory_profile']
  current_profile = current_data['memory_profile']
  
  <<~HTML
    <!DOCTYPE html>
    <html>
    <head>
        <title>Sentry Logging Memory Comparison Report</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
            .header { background: #f5f5f5; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
            .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
            .comparison-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
            .comparison-table th, .comparison-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            .comparison-table th { background-color: #f2f2f2; font-weight: bold; }
            .comparison-table tr:nth-child(even) { background-color: #f9f9f9; }
            .metric-comparison { display: flex; justify-content: space-between; margin: 10px 0; }
            .metric-box { flex: 1; margin: 0 10px; padding: 15px; border-radius: 5px; }
            .baseline { background: #e3f2fd; }
            .current { background: #f3e5f5; }
            .improvement { background: #e8f5e8; }
            .regression { background: #ffebee; }
            .neutral { background: #fff3e0; }
            .code-location { font-family: monospace; font-size: 11px; color: #666; }
            .commit-info { background: #f8f9fa; padding: 10px; border-radius: 3px; margin: 10px 0; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🔍 Sentry Logging Memory Comparison Report</h1>
            <p><strong>Generated:</strong> #{Time.now.strftime('%Y-%m-%d %H:%M:%S %Z')}</p>
        </div>

        <div class="section">
            <h2>📊 Comparison Overview</h2>
            <div class="metric-comparison">
                <div class="metric-box baseline">
                    <h3>Baseline</h3>
                    <p><strong>Commit:</strong> #{baseline_meta['git_info']['short_sha']}</p>
                    <p><strong>Branch:</strong> #{baseline_meta['git_info']['branch']}</p>
                    <p><strong>Date:</strong> #{baseline_meta['timestamp']}</p>
                    <p><strong>Iterations:</strong> #{baseline_meta['iterations']}</p>
                </div>
                <div class="metric-box current">
                    <h3>Current</h3>
                    <p><strong>Commit:</strong> #{current_meta['git_info']['short_sha']}</p>
                    <p><strong>Branch:</strong> #{current_meta['git_info']['branch']}</p>
                    <p><strong>Date:</strong> #{current_meta['timestamp']}</p>
                    <p><strong>Iterations:</strong> #{current_meta['iterations']}</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📈 Memory Usage Comparison</h2>
            <table class="comparison-table">
                <tr><th>Metric</th><th>Baseline</th><th>Current</th><th>Change</th></tr>
                <tr>
                    <td>Total Allocated Objects</td>
                    <td>#{baseline_profile['total_allocated'].to_s.reverse.gsub(/(\d{3})(?=\d)/, '\\1,').reverse}</td>
                    <td>#{current_profile['total_allocated'].to_s.reverse.gsub(/(\d{3})(?=\d)/, '\\1,').reverse}</td>
                    <td>#{format_change(baseline_profile['total_allocated'], current_profile['total_allocated'])}</td>
                </tr>
                <tr>
                    <td>Total Retained Objects</td>
                    <td>#{baseline_profile['total_retained'].to_s.reverse.gsub(/(\d{3})(?=\d)/, '\\1,').reverse}</td>
                    <td>#{current_profile['total_retained'].to_s.reverse.gsub(/(\d{3})(?=\d)/, '\\1,').reverse}</td>
                    <td>#{format_change(baseline_profile['total_retained'], current_profile['total_retained'])}</td>
                </tr>
                <tr>
                    <td>Total Allocated Memory</td>
                    <td>#{format_bytes(baseline_profile['total_allocated_memsize'])}</td>
                    <td>#{format_bytes(current_profile['total_allocated_memsize'])}</td>
                    <td>#{format_change(baseline_profile['total_allocated_memsize'], current_profile['total_allocated_memsize'], format_as_bytes: true)}</td>
                </tr>
                <tr>
                    <td>Total Retained Memory</td>
                    <td>#{format_bytes(baseline_profile['total_retained_memsize'])}</td>
                    <td>#{format_bytes(current_profile['total_retained_memsize'])}</td>
                    <td>#{format_change(baseline_profile['total_retained_memsize'], current_profile['total_retained_memsize'], format_as_bytes: true)}</td>
                </tr>
                <tr>
                    <td>Memory per Iteration</td>
                    <td>#{format_bytes(baseline_profile['total_allocated_memsize'] / baseline_meta['iterations'])}</td>
                    <td>#{format_bytes(current_profile['total_allocated_memsize'] / current_meta['iterations'])}</td>
                    <td>#{format_change(baseline_profile['total_allocated_memsize'] / baseline_meta['iterations'], current_profile['total_allocated_memsize'] / current_meta['iterations'], format_as_bytes: true)}</td>
                </tr>
            </table>
        </div>

        #{generate_class_comparison_section(baseline_profile, current_profile)}
        #{generate_location_comparison_section(baseline_profile, current_profile)}
        #{generate_commit_details_section(baseline_meta, current_meta)}

    </body>
    </html>
  HTML
end

def generate_class_comparison_section(baseline_profile, current_profile)
  # Convert arrays to hashes for easier lookup
  baseline_by_class = baseline_profile['allocated_objects_by_class'].to_h { |item| [item['data'], item['count']] }
  current_by_class = current_profile['allocated_objects_by_class'].to_h { |item| [item['data'], item['count']] }
  
  all_classes = (baseline_by_class.keys + current_by_class.keys).uniq.sort
  
  class_rows = all_classes.map do |klass|
    baseline_count = baseline_by_class[klass] || 0
    current_count = current_by_class[klass] || 0
    
    "<tr>" \
      "<td>#{klass}</td>" \
      "<td>#{baseline_count}</td>" \
      "<td>#{current_count}</td>" \
      "<td>#{format_change(baseline_count, current_count)}</td>" \
    "</tr>"
  end.join
  
  <<~HTML
    <div class="section">
        <h2>🧠 Object Allocation by Class Comparison</h2>
        <table class="comparison-table">
            <tr><th>Object Type</th><th>Baseline</th><th>Current</th><th>Change</th></tr>
            #{class_rows}
        </table>
    </div>
  HTML
end

def generate_location_comparison_section(baseline_profile, current_profile)
  # Get top locations from both runs
  baseline_locations = baseline_profile['allocated_objects_by_location'].first(10).to_h { |item| [item['data'], item['count']] }
  current_locations = current_profile['allocated_objects_by_location'].first(10).to_h { |item| [item['data'], item['count']] }
  
  all_locations = (baseline_locations.keys + current_locations.keys).uniq
  
  location_rows = all_locations.map do |location|
    baseline_count = baseline_locations[location] || 0
    current_count = current_locations[location] || 0
    
    next if baseline_count == 0 && current_count == 0
    
    "<tr>" \
      "<td class='code-location'>#{location}</td>" \
      "<td>#{baseline_count}</td>" \
      "<td>#{current_count}</td>" \
      "<td>#{format_change(baseline_count, current_count)}</td>" \
    "</tr>"
  end.compact.join
  
  <<~HTML
    <div class="section">
        <h2>📍 Top Allocation Locations Comparison</h2>
        <table class="comparison-table">
            <tr><th>Location</th><th>Baseline</th><th>Current</th><th>Change</th></tr>
            #{location_rows}
        </table>
    </div>
  HTML
end

def generate_commit_details_section(baseline_meta, current_meta)
  <<~HTML
    <div class="section">
        <h2>📋 Commit Details</h2>
        <div class="commit-info">
            <h3>Baseline Commit (#{baseline_meta['git_info']['short_sha']})</h3>
            <p><strong>Author:</strong> #{baseline_meta['git_info']['author']}</p>
            <p><strong>Date:</strong> #{baseline_meta['git_info']['date']}</p>
            <p><strong>Message:</strong> #{baseline_meta['git_info']['commit_message']}</p>
        </div>
        <div class="commit-info">
            <h3>Current Commit (#{current_meta['git_info']['short_sha']})</h3>
            <p><strong>Author:</strong> #{current_meta['git_info']['author']}</p>
            <p><strong>Date:</strong> #{current_meta['git_info']['date']}</p>
            <p><strong>Message:</strong> #{current_meta['git_info']['commit_message']}</p>
        </div>
    </div>
  HTML
end

# Main execution
if ARGV.length == 2
  baseline_file = ARGV[0]
  current_file = ARGV[1]
elsif ARGV.length == 0
  baseline_file, current_file = find_latest_benchmark_files
else
  puts "Usage: ruby benchmarks/logging_memory_comparison.rb [baseline_file] [current_file]"
  puts "       ruby benchmarks/logging_memory_comparison.rb  # Uses last two runs"
  exit 1
end

puts "=== Sentry Logging Memory Comparison ==="
puts "Baseline: #{File.basename(baseline_file)}"
puts "Current:  #{File.basename(current_file)}"
puts

# Load benchmark data
baseline_data = load_benchmark_data(baseline_file)
current_data = load_benchmark_data(current_file)

# Generate comparison report
output_dir = 'tmp'
FileUtils.mkdir_p(output_dir)
comparison_file = File.join(output_dir, 'logging_memory_comparison.html')

html_content = generate_comparison_report(baseline_data, current_data)
File.write(comparison_file, html_content)

puts "✓ Comparison report generated: #{comparison_file}"
puts
puts "=== Summary ==="
baseline_profile = baseline_data['memory_profile']
current_profile = current_data['memory_profile']

puts "Total Allocated Objects: #{format_change(baseline_profile['total_allocated'], current_profile['total_allocated'])}"
puts "Total Retained Objects:  #{format_change(baseline_profile['total_retained'], current_profile['total_retained'])}"
puts "Total Allocated Memory:  #{format_change(baseline_profile['total_allocated_memsize'], current_profile['total_allocated_memsize'], format_as_bytes: true)}"
puts "Total Retained Memory:   #{format_change(baseline_profile['total_retained_memsize'], current_profile['total_retained_memsize'], format_as_bytes: true)}"
puts
puts "Open #{comparison_file} in your browser to view the detailed comparison analysis."
