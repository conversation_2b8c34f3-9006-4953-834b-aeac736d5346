#!/usr/bin/env ruby
# frozen_string_literal: true

# Installation script for sentry-bench gem
# This script builds and installs the sentry-bench gem locally

puts "=== Installing sentry-bench gem ==="
puts

# Check if we're in the right directory
unless File.exist?('sentry-bench.gemspec')
  puts "Error: sentry-bench.gemspec not found."
  puts "Please run this script from the sentry-ruby directory."
  exit 1
end

# Build the gem
puts "Building gem..."
result = system('gem build sentry-bench.gemspec')

unless result
  puts "Error: Failed to build gem."
  exit 1
end

# Find the built gem file
gem_file = Dir.glob('sentry-bench-*.gem').first

unless gem_file
  puts "Error: Built gem file not found."
  exit 1
end

puts "✓ Gem built: #{gem_file}"

# Install the gem
puts "Installing gem..."
result = system("gem install #{gem_file}")

unless result
  puts "Error: Failed to install gem."
  exit 1
end

puts "✓ Gem installed successfully!"
puts

# Test the installation
puts "Testing installation..."
result = system('sentry-bench --version')

if result
  puts "✓ Installation test passed!"
  puts
  puts "You can now use sentry-bench from anywhere:"
  puts "  sentry-bench --help"
  puts "  sentry-bench -i 1000"
  puts "  sentry-bench --open"
  puts
  puts "To compare benchmark results:"
  puts "  ruby bin/sentry-bench-compare [data_directory]"
else
  puts "⚠️  Installation test failed. The gem may not be in your PATH."
  puts "Try running: gem environment"
end

# Clean up
puts "Cleaning up..."
File.delete(gem_file) if File.exist?(gem_file)
puts "✓ Cleanup complete"

puts
puts "=== Installation Complete ==="
