# frozen_string_literal: true

module SentryBench
  class GitManager
    attr_reader :original_state, :target_sha, :verbose

    def initialize(target_sha: nil, verbose: true)
      @target_sha = target_sha
      @verbose = verbose
      @original_state = nil
      @switched = false
    end

    def with_commit_sha(&block)
      return yield unless target_sha

      puts_verbose "=== Git Commit Management ==="
      
      # Validate git repository
      unless git_repository?
        raise Error, "Not in a git repository. Cannot switch to commit SHA."
      end

      # Check for uncommitted changes
      if has_uncommitted_changes?
        raise Error, "Working directory has uncommitted changes. Please commit or stash changes before using --commit-sha."
      end

      # Save current state
      save_current_state

      begin
        # Switch to target commit
        switch_to_commit
        puts_verbose "✓ Switched to commit #{target_sha}"
        puts_verbose

        # Execute the block
        yield
      ensure
        # Always restore original state
        restore_original_state
      end
    end

    private

    def git_repository?
      sys_command("git rev-parse --git-dir") != "unknown"
    end

    def has_uncommitted_changes?
      # Check for staged changes
      staged = sys_command("git diff --cached --quiet")
      # Check for unstaged changes
      unstaged = sys_command("git diff --quiet")
      # Check for untracked files
      untracked = sys_command("git ls-files --others --exclude-standard")
      
      staged == "unknown" || unstaged == "unknown" || !untracked.empty?
    end

    def save_current_state
      puts_verbose "Saving current git state..."
      
      @original_state = {
        branch: sys_command("git rev-parse --abbrev-ref HEAD"),
        sha: sys_command("git rev-parse HEAD"),
        is_detached: sys_command("git symbolic-ref -q HEAD") == "unknown"
      }
      
      puts_verbose "  Current branch: #{@original_state[:branch]}"
      puts_verbose "  Current SHA: #{@original_state[:sha][0..7]}"
      puts_verbose "  Detached HEAD: #{@original_state[:is_detached]}"
    end

    def switch_to_commit
      puts_verbose "Switching to commit #{target_sha}..."
      
      # Validate that the commit exists
      unless commit_exists?(target_sha)
        raise Error, "Commit SHA '#{target_sha}' does not exist in this repository."
      end

      # Checkout the specific commit
      result = system("git checkout #{target_sha} 2>/dev/null")
      unless result
        raise Error, "Failed to checkout commit #{target_sha}. Please check that the SHA is valid."
      end

      @switched = true
    end

    def restore_original_state
      return unless @switched && @original_state

      puts_verbose "Restoring original git state..."
      
      if @original_state[:is_detached]
        # If we were originally in detached HEAD, go back to that SHA
        result = system("git checkout #{@original_state[:sha]} 2>/dev/null")
        puts_verbose "  Restored to detached HEAD: #{@original_state[:sha][0..7]}"
      else
        # If we were on a branch, go back to that branch
        result = system("git checkout #{@original_state[:branch]} 2>/dev/null")
        puts_verbose "  Restored to branch: #{@original_state[:branch]}"
      end

      unless result
        puts_verbose "⚠️  Warning: Failed to restore original git state."
        puts_verbose "   You may need to manually checkout: #{@original_state[:branch] || @original_state[:sha]}"
      else
        puts_verbose "✓ Git state restored successfully"
      end
      
      puts_verbose
    end

    def commit_exists?(sha)
      result = sys_command("git cat-file -e #{sha}^{commit}")
      result != "unknown"
    end

    def puts_verbose(message = "")
      puts message if verbose
    end

    def sys_command(command)
      result = `#{command} 2>/dev/null`.strip
      $?.success? ? result : "unknown"
    end
  end
end
