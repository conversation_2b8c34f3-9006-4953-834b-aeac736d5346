# frozen_string_literal: true

require 'optparse'

module SentryBench
  class CLI
    def self.run(args = ARGV)
      new.run(args)
    end

    def run(args)
      options = parse_options(args)

      if options[:help]
        puts @parser
        return
      end

      if options[:version]
        puts "sentry-bench version #{SentryBench::VERSION}"
        return
      end

      begin
        profiler = Profiler.new(options)
        report_file = profiler.run

        if options[:open] && report_file
          open_report(report_file)
        end
      rescue Error => e
        puts "Error: #{e.message}"
        exit 1
      rescue => e
        puts "Unexpected error: #{e.message}"
        puts e.backtrace if options[:verbose]
        exit 1
      end
    end

    private

    def parse_options(args)
      options = {
        iterations: 1000,
        output_dir: 'tmp',
        verbose: true,
        open: false
      }

      @parser = OptionParser.new do |opts|
        opts.banner = "Usage: sentry-bench [options]"
        opts.separator ""
        opts.separator "Options:"

        opts.on("-i", "--iterations N", Integer, "Number of iterations to run (default: 1000)") do |n|
          options[:iterations] = n
        end

        opts.on("-o", "--output DIR", String, "Output directory for reports (default: tmp)") do |dir|
          options[:output_dir] = dir
        end

        opts.on("-r", "--report FILE", String, "Report file path (default: output_dir/sentry_bench_report.html)") do |file|
          options[:report_file] = file
        end

        opts.on("--open", "Open the report in browser after generation") do
          options[:open] = true
        end

        opts.on("-q", "--quiet", "Run quietly (suppress output)") do
          options[:verbose] = false
        end

        opts.on("-v", "--verbose", "Run with verbose output") do
          options[:verbose] = true
        end

        opts.on("--version", "Show version") do
          options[:version] = true
        end

        opts.on("--commit-sha SHA", String, "Checkout specific commit SHA before running benchmark") do |sha|
          options[:commit_sha] = sha
        end

        opts.on("-h", "--help", "Show this help") do
          options[:help] = true
        end

        opts.separator ""
        opts.separator "Examples:"
        opts.separator "  sentry-bench                           # Run with default settings"
        opts.separator "  sentry-bench -i 5000 -o benchmarks    # Run 5000 iterations, output to benchmarks/"
        opts.separator "  sentry-bench --open                   # Run and open report in browser"
        opts.separator "  sentry-bench -q                       # Run quietly"
        opts.separator "  sentry-bench --commit-sha abc123      # Run benchmark on specific commit"
        opts.separator ""
        opts.separator "The tool will:"
        opts.separator "  - Run memory profiling on Sentry logging operations"
        opts.separator "  - Generate an HTML report with detailed analysis"
        opts.separator "  - Save benchmark data as JSON for comparison"
        opts.separator "  - Track git commit information for version comparison"
        opts.separator "  - Optionally checkout specific commit SHA for testing"
      end

      begin
        @parser.parse!(args)
      rescue OptionParser::InvalidOption => e
        puts "Error: #{e.message}"
        puts @parser
        exit 1
      end

      options
    end

    def help_text
      <<~HELP
        sentry-bench - Performance benchmarking tool for Sentry Ruby SDK

        This tool measures memory usage and performance of the Sentry Ruby SDK
        logging functionality across different git commits.

        USAGE:
            sentry-bench [options]

        OPTIONS:
            -i, --iterations N       Number of iterations to run (default: 1000)
            -o, --output DIR         Output directory for reports (default: tmp)
            -r, --report FILE        Report file path (default: output_dir/sentry_bench_report.html)
                --open               Open the report in browser after generation
                --commit-sha SHA     Checkout specific commit SHA before running benchmark
            -q, --quiet              Run quietly (suppress output)
            -v, --verbose            Run with verbose output
                --version            Show version
            -h, --help               Show this help

        EXAMPLES:
            sentry-bench                           # Run with default settings
            sentry-bench -i 5000 -o benchmarks    # Run 5000 iterations, output to benchmarks/
            sentry-bench --open                   # Run and open report in browser
            sentry-bench -q                       # Run quietly
            sentry-bench --commit-sha abc123      # Run benchmark on specific commit

        OUTPUT:
            The tool generates:
            - HTML report with comprehensive memory analysis
            - JSON data files for programmatic analysis
            - Git commit tracking for version comparison

        WORKFLOW:
            1. Configure Sentry with a benchmark transport (no network calls)
            2. Optionally checkout specific commit SHA for testing
            3. Run memory profiling on varied logging operations
            4. Analyze memory allocation and retention patterns
            5. Generate detailed HTML report with recommendations
            6. Save benchmark data with git commit information
            7. Restore original git state (when using --commit-sha)

        GIT COMMIT TESTING:
            Use --commit-sha to benchmark specific commits:
            - Requires clean working directory (no uncommitted changes)
            - Automatically saves and restores current git state
            - Loads sentry-ruby gem from local repository path
            - Useful for performance regression testing

        For more information, visit: https://github.com/getsentry/sentry-ruby
      HELP
    end

    def open_report(report_file)
      if File.exist?(report_file)
        case RbConfig::CONFIG['host_os']
        when /mswin|mingw|cygwin/
          system("start #{report_file}")
        when /darwin/
          system("open #{report_file}")
        when /linux|bsd/
          system("xdg-open #{report_file}")
        else
          puts "Report generated: #{report_file}"
          puts "Please open this file in your browser to view the results."
        end
      else
        puts "Report file not found: #{report_file}"
      end
    end
  end
end
